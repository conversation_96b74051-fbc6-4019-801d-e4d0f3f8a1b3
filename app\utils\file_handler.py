"""
文件上传处理工具模块
提供文件验证、图片加载等功能
"""
import os
import uuid
from io import BytesIO
from typing import Optional, <PERSON>ple
from pathlib import Path
from datetime import datetime
from PIL import Image
from fastapi import UploadFile, HTTPException
from app.config import get_settings
from app.core.logging import get_logger
from app.utils.minio_client import upload_image_to_minio

logger = get_logger(__name__)


class FileHandler:
    """文件处理工具类"""
    
    def __init__(self):
        # 获取配置
        try:
            settings = get_settings()
            self.max_file_size = settings.upload.max_file_size
            self.allowed_extensions = settings.upload.allowed_extensions
        except:
            # 如果无法获取配置，使用默认值
            self.max_file_size = 10 * 1024 * 1024  # 10MB
            self.allowed_extensions = ["jpg", "jpeg", "png", "gif", "pdf"]
    
    def validate_file(self, file: UploadFile) -> Optional[str]:
        """
        验证上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Optional[str]: 如果验证失败返回错误信息，否则返回None
        """
        try:
            # 检查文件是否为空
            if not file.filename:
                return "未选择文件"
            
            # 检查文件扩展名
            file_extension = Path(file.filename).suffix.lower().lstrip('.')
            if file_extension not in self.allowed_extensions:
                return f"不支持的文件格式，支持的格式: {', '.join(self.allowed_extensions)}"
            
            return None
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return f"文件验证失败: {str(e)}"
    
    async def load_image_from_upload(self, file: UploadFile) -> Image.Image:
        """
        直接从上传文件加载图片（不保存到磁盘）
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Image.Image: PIL图片对象
            
        Raises:
            HTTPException: 图片加载失败
        """
        try:
            # 验证文件
            validation_error = self.validate_file(file)
            if validation_error:
                raise HTTPException(
                    status_code=400,
                    detail=validation_error
                )
            
            # 读取文件内容
            content = await file.read()
            
            # 再次检查文件大小
            if len(content) > self.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)"
                )
            
            # 直接从内存加载图片
            image_stream = BytesIO(content)
            image = Image.open(image_stream)
            image.load()  # 确保图片数据加载到内存
            
            logger.info(f"图片从上传文件加载成功: {file.filename}, 格式: {image.format}, 尺寸: {image.size}")
            return image
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"从上传文件加载图片失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"图片加载失败: {str(e)}"
            )
    
    async def save_image_to_minio(
        self, 
        file: UploadFile, 
        folder: str = "temp"
    ) -> Optional[Tuple[str, str, str, datetime]]:
        """
        保存图片到 MinIO 并返回图片信息
        
        Args:
            file: 上传的文件对象
            folder: 存储文件夹 (company-templates, ocr-tasks, temp)
            
        Returns:
            Optional[Tuple[str, str, str, datetime]]: (image_uuid, minio_path, original_filename, upload_time) 或 None
        """
        try:
            # 验证文件
            validation_error = self.validate_file(file)
            if validation_error:
                logger.error(f"文件验证失败: {validation_error}")
                return None
            
            # 上传到 MinIO
            result = await upload_image_to_minio(file, folder)
            if not result:
                logger.error("图片上传到 MinIO 失败")
                return None
            
            image_uuid, minio_path, original_filename = result
            upload_time = datetime.now()
            
            logger.info(f"图片保存到 MinIO 成功: {minio_path}")
            return image_uuid, minio_path, original_filename, upload_time
            
        except Exception as e:
            logger.error(f"保存图片到 MinIO 失败: {e}")
            return None


# 全局文件处理器实例
file_handler = FileHandler()


async def validate_and_load_image(file: UploadFile) -> Image.Image:
    """
    验证并加载图片的便捷函数
    
    Args:
        file: 上传的文件对象
        
    Returns:
        Image.Image: PIL图片对象
        
    Raises:
        HTTPException: 验证或加载失败
    """
    return await file_handler.load_image_from_upload(file) 