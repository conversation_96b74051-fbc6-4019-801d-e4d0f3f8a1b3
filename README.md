# FastAPI OCR 项目

一个功能完整的 FastAPI Web API 框架，支持数据模型校验、异步日志、配置管理、ORM 数据拦截处理和跨域支持。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制配置文件模板
cp env.example .env

# 编辑配置文件
# Windows
notepad .env
# Linux/Mac
nano .env
```

### 3. 启动应用

```bash
# 开发环境启动（默认）
python main.py

# 或者指定环境
python main.py -env dev

# 生产环境启动
python main.py -env prod --workers 4

# 测试环境启动
python main.py -env test
```

### 4. 访问应用

- **应用首页**: http://localhost:8000
- **API 文档 (Swagger)**: http://localhost:8000/docs
- **API 文档 (ReDoc)**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health
- **应用信息**: http://localhost:8000/info

## 📁 项目结构

```
ocr_main/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── config/            # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py    # 配置类
│   │   └── database.py    # 数据库配置
│   ├── core/              # 核心服务
│   │   ├── __init__.py
│   │   ├── logging.py     # 异步日志系统
│   │   └── middleware.py  # 统一中间件
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── base.py        # 基础模型类
│   ├── schemas/           # Pydantic 模式
│   │   └── __init__.py
│   ├── api/               # API 路由
│   │   └── v1/
│   │       └── __init__.py
│   └── utils/             # 工具函数
│       └── __init__.py

├── main.py                # 启动入口和FastAPI应用定义
├── requirements.txt       # 依赖包
├── env.example           # 配置模板
├── .env                  # 环境配置（需要创建）
└── README.md             # 项目说明
```

## ⚙️ 配置说明

### 环境变量配置

项目支持通过环境变量进行配置，主要配置项包括：

#### 应用配置
- `APP_NAME`: 应用名称
- `APP_VERSION`: 应用版本
- `APP_ENV`: 运行环境 (dev/test/prod)
- `DEBUG`: 调试模式
- `HOST`: 服务器主机地址
- `PORT`: 服务器端口

#### 数据库配置
- `DATABASE_URL`: 数据库连接URL
- `DATABASE_ECHO`: 是否输出SQL日志

#### 日志配置
- `LOG_LEVEL`: 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `LOG_FILE`: 日志文件路径（可选）
- `LOG_ROTATION`: 日志轮转周期
- `LOG_RETENTION`: 日志保留时间

#### CORS 配置
- `CORS_ORIGINS`: 允许的跨域来源
- `CORS_CREDENTIALS`: 是否允许携带凭证
- `CORS_METHODS`: 允许的HTTP方法
- `CORS_HEADERS`: 允许的请求头

#### 文件上传配置
- `UPLOAD_DIR`: 上传目录
- `MAX_FILE_SIZE`: 最大文件大小
- `ALLOWED_EXTENSIONS`: 允许的文件扩展名

### 配置文件位置

1. **开发环境**: `.env` 文件
2. **生产环境**: 环境变量或 `.env` 文件
3. **Docker**: 环境变量

## 🎯 主要功能

### 1. 数据模型校验
- 基于 Pydantic 的自动数据验证
- 类型提示和自动文档生成
- 请求和响应模型定义

### 2. 异步日志系统
- 基于 Loguru 的高性能异步日志
- 多级别日志记录
- 自动日志轮转和压缩
- 结构化日志格式

### 3. 配置管理
- 多环境配置支持
- 环境变量自动映射
- 类型安全的配置类

### 4. ORM 数据拦截
- SQLAlchemy 事件监听
- 自动时间戳管理
- 软删除支持
- 统一数据处理逻辑

### 5. 跨域支持
- CORS 中间件配置
- 灵活的跨域策略
- 支持预检请求

### 6. 健康检查
- 应用状态监控
- 数据库连接检查
- 系统信息获取

## 🔧 命令行参数

```bash
python main.py [选项]

选项:
  -env {dev,test,prod}  运行环境 (默认: dev)
  --host HOST          服务器主机地址 (默认: 0.0.0.0)
  --port PORT          服务器端口 (默认: 8000)
  --reload             启用自动重载（仅开发环境）
  --workers WORKERS    工作进程数量（生产环境）
  -h, --help           显示帮助信息
```

## 📊 环境差异

### 开发环境 (dev)
- 自动重载启用
- 详细日志输出
- SQLite 数据库
- Swagger 文档可用

### 测试环境 (test)
- 测试数据库
- 简化日志输出
- 无自动重载

### 生产环境 (prod)
- 多进程部署
- 性能优化配置
- PostgreSQL 数据库
- Swagger 文档禁用

## 🛠️ 开发指南

### 添加新的 API 端点

1. 在 `app/api/v1/endpoints/` 下创建新的路由文件
2. 在 `app/api/v1/__init__.py` 中注册路由
3. 在 `app/app.py` 中包含路由组

### 添加新的数据模型

1. 在 `app/models/` 下创建模型文件
2. 继承 `BaseModel` 或 `FullAuditModel`
3. 在 `app/models/__init__.py` 中导出模型

### 添加新的配置项

1. 在 `app/config/settings.py` 中添加配置字段
2. 在 `env.example` 中添加配置说明
3. 更新文档说明

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t fastapi-ocr .

# 运行容器
docker run -p 8000:8000 -e APP_ENV=prod fastapi-ocr
```

### 生产环境部署

```bash
# 使用 gunicorn + uvicorn
gunicorn app.app:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

# 或者使用内置启动器
python main.py -env prod --workers 4
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！ 