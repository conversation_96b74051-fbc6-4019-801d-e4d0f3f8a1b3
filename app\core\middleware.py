"""
统一数据拦截中间件
实现请求响应日志记录、异常处理、性能监控和数据拦截
"""
import time
import uuid
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from sqlalchemy import event
from sqlalchemy.ext.asyncio import AsyncSession

from .logging import get_logger
from ..config import get_settings

logger = get_logger(__name__)


class DataInterceptorMiddleware(BaseHTTPMiddleware):
    """统一数据拦截中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.setup_db_event_listeners()
    
    def setup_db_event_listeners(self):
        """设置数据库事件监听器"""
        
        @event.listens_for(AsyncSession, 'before_insert')
        def before_insert_listener(mapper, connection, target):
            """插入前数据拦截"""
            # 自动添加创建时间
            if hasattr(target, 'created_at') and not target.created_at:
                from datetime import datetime
                target.created_at = datetime.utcnow()
            
            # 自动添加更新时间
            if hasattr(target, 'updated_at'):
                from datetime import datetime
                target.updated_at = datetime.utcnow()
            
            # 记录操作日志
            logger.info(f"数据插入: {target.__class__.__name__}")
        
        @event.listens_for(AsyncSession, 'before_update')
        def before_update_listener(mapper, connection, target):
            """更新前数据拦截"""
            # 自动更新修改时间
            if hasattr(target, 'updated_at'):
                from datetime import datetime
                target.updated_at = datetime.utcnow()
            
            # 记录操作日志
            logger.info(f"数据更新: {target.__class__.__name__}")
        
        @event.listens_for(AsyncSession, 'before_delete')
        def before_delete_listener(mapper, connection, target):
            """删除前数据拦截"""
            # 软删除处理
            if hasattr(target, 'is_deleted'):
                target.is_deleted = True
                if hasattr(target, 'deleted_at'):
                    from datetime import datetime
                    target.deleted_at = datetime.utcnow()
                logger.info(f"软删除: {target.__class__.__name__}")
            else:
                logger.info(f"硬删除: {target.__class__.__name__}")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """请求处理中间件"""
        start_time = time.time()
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求信息
        logger.info(
            f"请求开始 [{request_id}] {request.method} {request.url.path}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent", ""),
            }
        )
        
        # 添加请求ID到请求状态
        request.state.request_id = request_id
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"请求完成 [{request_id}] {response.status_code} - {process_time:.4f}s",
                extra={
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "process_time": process_time,
                }
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as exc:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录异常信息
            logger.error(
                f"请求异常 [{request_id}] {type(exc).__name__}: {str(exc)} - {process_time:.4f}s",
                extra={
                    "request_id": request_id,
                    "exception": type(exc).__name__,
                    "error_message": str(exc),
                    "process_time": process_time,
                },
                exc_info=True
            )
            
            # 返回统一的错误响应
            return await self.handle_exception(request, exc, request_id)
    
    async def handle_exception(self, request: Request, exc: Exception, request_id: str) -> JSONResponse:
        """统一异常处理"""
        from ..utils.response import (
            error_response, 
            validation_error_response,
            database_error_response,
            internal_error_response,
            create_json_response
        )
        from ..schemas.response import StatusCode
        
        # HTTP异常处理
        if isinstance(exc, HTTPException):
            # 检查是否是统一格式的异常
            if isinstance(exc.detail, dict) and "code" in exc.detail:
                # 已经是统一格式，直接返回
                return JSONResponse(
                    status_code=exc.status_code,
                    content=exc.detail,
                    headers={"X-Request-ID": request_id}
                )
            else:
                # 转换为统一格式
                response = error_response(
                    message=str(exc.detail),
                    code=exc.status_code
                )
                return create_json_response(response, exc.status_code)
        
        # 获取配置
        try:
            settings = get_settings()
            is_production = settings.is_production
        except:
            is_production = False

        # 数据库异常处理
        if "sqlalchemy" in str(type(exc)).lower():
            message = "数据库操作失败" if is_production else str(exc)
            response = database_error_response(message=message)
            return create_json_response(response, 500)

        # 验证异常处理
        if "validation" in str(type(exc)).lower():
            detail = str(exc) if not is_production else None
            response = validation_error_response(
                message="数据验证失败",
                data={"detail": detail} if detail else None
            )
            return create_json_response(response, 422)

        # 通用异常处理
        message = "服务器内部错误" if is_production else str(exc)
        response = internal_error_response(message=message)
        return create_json_response(response, 500)