"""
异步日志系统
基于 Loguru 实现多级别日志处理和文件轮转
"""
import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from ..config import get_settings


class LoggingConfig:
    """日志配置类"""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """配置日志系统"""
        # 移除默认处理器
        logger.remove()

        # 获取配置
        try:
            settings = get_settings()
        except:
            # 如果无法获取配置，使用默认值
            settings = None

        # 控制台日志配置
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )

        log_level = settings.logging.level if settings else "INFO"

        logger.add(
            sys.stdout,
            format=console_format,
            level=log_level,
            colorize=True,
            enqueue=True,  # 异步日志
            backtrace=True,
            diagnose=True,
        )

        # 文件日志配置
        if settings:
            # 默认日志文件配置
            self._setup_default_file_logging()
    
    def _setup_file_logging(self, log_file: str):
        """配置指定的文件日志"""
        import platform

        # 获取配置
        try:
            settings = get_settings()
            log_level = settings.logging.level
            log_rotation = settings.logging.rotation
            log_retention = settings.logging.retention
        except:
            # 如果无法获取配置，使用默认值
            log_level = "INFO"
            log_rotation = "1 day"
            log_retention = "30 days"

        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )

        # Windows 系统特殊处理
        is_windows = platform.system().lower() == "windows"

        logger.add(
            log_file,
            format=file_format,
            level=log_level,
            rotation=log_rotation if not is_windows else "10 MB",  # Windows 使用大小轮转
            retention=log_retention,
            compression="zip" if not is_windows else None,  # Windows 暂时不压缩
            enqueue=True,
            backtrace=True,
            diagnose=True,
            catch=True,  # 捕获日志处理异常
        )
    
    def _setup_default_file_logging(self):
        """配置默认文件日志"""
        import platform

        # 获取配置
        try:
            settings = get_settings()
            log_level = settings.logging.level
            log_rotation = settings.logging.rotation
            log_retention = settings.logging.retention
        except:
            # 如果无法获取配置，使用默认值
            log_level = "INFO"
            log_rotation = "1 day"
            log_retention = "30 days"

        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 应用日志文件
        app_log_file = log_dir / "app.log"
        error_log_file = log_dir / "error.log"

        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )

        # Windows 系统特殊处理
        is_windows = platform.system().lower() == "windows"

        # 应用日志（所有级别）
        logger.add(
            app_log_file,
            format=file_format,
            level=log_level,
            rotation=log_rotation if not is_windows else "10 MB",  # Windows 使用大小轮转
            retention=log_retention,
            compression="zip" if not is_windows else None,  # Windows 暂时不压缩
            enqueue=True,
            backtrace=True,
            diagnose=True,
            catch=True,  # 捕获日志处理异常
        )

        # 错误日志（仅ERROR及以上级别）
        logger.add(
            error_log_file,
            format=file_format,
            level="ERROR",
            rotation=log_rotation if not is_windows else "5 MB",  # Windows 使用大小轮转
            retention=log_retention,
            compression="zip" if not is_windows else None,  # Windows 暂时不压缩
            enqueue=True,
            backtrace=True,
            diagnose=True,
            catch=True,  # 捕获日志处理异常
        )


# 全局日志配置实例
logging_config = LoggingConfig()


def get_logger(name: Optional[str] = None):
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logger: 日志记录器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


# 日志装饰器
def log_function_call(func):
    """
    函数调用日志装饰器
    记录函数的调用和执行时间
    """
    import functools
    import time
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        function_logger = get_logger(func.__module__)
        
        function_logger.info(f"调用函数: {func.__name__}")
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            function_logger.info(f"函数 {func.__name__} 执行完成，耗时: {execution_time:.4f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            function_logger.error(f"函数 {func.__name__} 执行失败，耗时: {execution_time:.4f}秒，错误: {str(e)}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        function_logger = get_logger(func.__module__)
        
        function_logger.info(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            function_logger.info(f"函数 {func.__name__} 执行完成，耗时: {execution_time:.4f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            function_logger.error(f"函数 {func.__name__} 执行失败，耗时: {execution_time:.4f}秒，错误: {str(e)}")
            raise
    
    # 检查是否为异步函数
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


# 导出常用的日志函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    logger.info(message, **kwargs)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    logger.warning(message, **kwargs)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    logger.error(message, **kwargs)


def log_debug(message: str, **kwargs):
    """记录调试日志"""
    logger.debug(message, **kwargs)


def log_critical(message: str, **kwargs):
    """记录严重错误日志"""
    logger.critical(message, **kwargs) 