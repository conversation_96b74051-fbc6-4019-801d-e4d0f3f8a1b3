# FastAPI OCR 项目配置管理指南

## 概述

本项目使用基于 **Typer** 的配置管理系统，支持多环境配置和多种配置文件格式（JSON 和 INI）。

## 配置文件结构

### 支持的环境
- `dev` - 开发环境
- `prod` - 生产环境  
- `test` - 测试环境

### 支持的配置文件格式
- **JSON 格式**: `config/{env}.json`
- **INI 格式**: `config/{env}.ini`
- **自动检测**: 优先使用 JSON，如果不存在则使用 INI

## 配置文件示例

### JSON 格式 (config/dev.json)
```json
{
  "app": {
    "name": "FastAPI OCR Project",
    "version": "1.0.0",
    "debug": true,
    "host": "0.0.0.0",
    "port": 8000
  },
  "database": {
    "url": "sqlite+aiosqlite:///./dev.db",
    "echo": true,
    "pool_size": 10,
    "max_overflow": 20
  },
  "redis": {
    "url": "redis://localhost:6379/0"
  },
  "logging": {
    "level": "DEBUG",
    "rotation": "1 day",
    "retention": "30 days"
  },
  "cors": {
    "origins": ["http://localhost:3000", "http://localhost:8080"],
    "credentials": true,
    "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "headers": ["*"]
  },
  "upload": {
    "dir": "uploads",
    "max_file_size": 10485760,
    "allowed_extensions": ["jpg", "jpeg", "png", "gif", "pdf"]
  },
  "minio": {
    "endpoint": "localhost:9000",
    "access_key": "minioadmin",
    "secret_key": "minioadmin",
    "secure": false,
    "bucket_name": "ocr-images",
    "enabled": true
  }
}
```

### INI 格式 (config/dev.ini)
```ini
[app]
name = FastAPI OCR Project
version = 1.0.0
debug = true
host = 0.0.0.0
port = 8000

[database]
url = sqlite+aiosqlite:///./dev.db
echo = true
pool_size = 10
max_overflow = 20

[redis]
url = redis://localhost:6379/0

[logging]
level = DEBUG
rotation = 1 day
retention = 30 days

[cors]
origins = http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
credentials = true
methods = GET,POST,PUT,DELETE,OPTIONS
headers = *

[upload]
dir = uploads
max_file_size = 10485760
allowed_extensions = jpg,jpeg,png,gif,pdf

[minio]
endpoint = localhost:9000
access_key = minioadmin
secret_key = minioadmin
secure = false
bucket_name = ocr-images
enabled = true
```

## 命令行使用

### 1. 查看帮助
```bash
python main.py --help
```

### 2. 启动服务器
```bash
# 使用默认开发环境
python main.py serve

# 指定环境
python main.py serve --env prod

# 指定配置格式
python main.py serve --env dev --config-format json

# 覆盖配置参数
python main.py serve --env dev --host 127.0.0.1 --port 8080

# 生产环境启动（多进程）
python main.py serve --env prod --workers 4
```

### 3. 查看配置信息
```bash
# 查看开发环境配置
python main.py config --env dev

# 查看生产环境配置
python main.py config --env prod

# 指定配置格式
python main.py config --env dev --config-format ini
```

### 4. 初始化数据库
```bash
# 初始化开发环境数据库
python main.py init-db --env dev

# 初始化生产环境数据库
python main.py init-db --env prod
```

### 5. 查看版本信息
```bash
python main.py version
```

## 配置优先级

1. **命令行参数** - 最高优先级
2. **配置文件** - 中等优先级
3. **默认值** - 最低优先级

例如：
```bash
# 配置文件中 port = 8000，但命令行指定 --port 9000
# 最终使用 port = 9000
python main.py serve --env dev --port 9000
```

## 环境变量支持

虽然主要使用配置文件，但系统仍支持通过环境变量覆盖配置：

```bash
# Windows
set DATABASE_URL=mysql://user:pass@localhost/db
python main.py serve --env dev

# Linux/Mac
export DATABASE_URL=mysql://user:pass@localhost/db
python main.py serve --env dev
```

## 配置验证

系统使用 Pydantic 进行配置验证，确保：
- 数据类型正确
- 必需字段存在
- 值在有效范围内

如果配置验证失败，系统会显示详细的错误信息。

## 最佳实践

1. **开发环境**: 使用 SQLite 数据库，启用调试模式
2. **生产环境**: 使用 MySQL/PostgreSQL，禁用调试模式，启用多进程
3. **测试环境**: 使用独立的测试数据库，简化配置
4. **敏感信息**: 生产环境的敏感信息（如数据库密码）建议使用环境变量
5. **配置文件**: 建议使用 JSON 格式，更易于维护和版本控制
